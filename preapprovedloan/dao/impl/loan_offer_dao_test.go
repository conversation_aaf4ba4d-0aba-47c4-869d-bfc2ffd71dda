package impl_test

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	cacheMocks "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

var (
	loanOfferSample1 = &preapprovedloanPb.LoanOffer{
		Id:               "loan-offer-id-1",
		ActorId:          "actor-1",
		VendorOfferId:    "vendor-offer-id-2",
		Vendor:           preapprovedloanPb.Vendor_FEDERAL,
		OfferConstraints: &preapprovedloanPb.OfferConstraints{},
		ProcessingInfo:   &preapprovedloanPb.OfferProcessingInfo{},
		ValidSince: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		ValidTill: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		DeactivatedAt: &timestampPb.Timestamp{
			Seconds: 0,
			Nanos:   0,
		},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanOfferType:                  preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}

	loanOfferSample2 = &preapprovedloanPb.LoanOffer{
		Id:                             "loan-offer-id-1",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-1",
		Vendor:                         preapprovedloanPb.Vendor_FEDERAL,
		OfferConstraints:               &preapprovedloanPb.OfferConstraints{},
		ProcessingInfo:                 &preapprovedloanPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}

	loanOfferSample3 = &preapprovedloanPb.LoanOffer{
		Id:                             "random-loan-offer-id-3",
		ActorId:                        "random-actor-3",
		VendorOfferId:                  "vendor-offer-id-3",
		Vendor:                         preapprovedloanPb.Vendor_FEDERAL,
		OfferConstraints:               &preapprovedloanPb.OfferConstraints{},
		ProcessingInfo:                 &preapprovedloanPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-3",
		LoanOfferType:                  preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	loanOfferSample4 = &preapprovedloanPb.LoanOffer{
		Id:                             "loan-offer-id-2",
		ActorId:                        "actor-1",
		VendorOfferId:                  "vendor-offer-id-3",
		Vendor:                         preapprovedloanPb.Vendor_FEDERAL,
		OfferConstraints:               &preapprovedloanPb.OfferConstraints{},
		ProcessingInfo:                 &preapprovedloanPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		DeactivatedAt: &timestampPb.Timestamp{
			Seconds: 1692988200,
		},
		LoanProgram:   preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType: preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	loanOfferSample5 = &preapprovedloanPb.LoanOffer{
		Id:                             "loan-offer-id-3",
		ActorId:                        "actor-3",
		VendorOfferId:                  "vendor-offer-id-4",
		Vendor:                         preapprovedloanPb.Vendor_FEDERAL,
		OfferConstraints:               &preapprovedloanPb.OfferConstraints{},
		ProcessingInfo:                 &preapprovedloanPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-1",
		LoanProgram:                    preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		LoanOfferType:                  preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
)

func TestCrdbLoanOfferDao_Create(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx       context.Context
		loanOffer *preapprovedloanPb.LoanOffer
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "Successful Creation Flow",
			args: args{
				ctx:       context.Background(),
				loanOffer: loanOfferSample1,
			},
			want:    loanOfferSample1,
			wantErr: false,
		},
		// TODO(Kantikumar): Add duplicate create test case when conditional index is added
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				ctr := gomock.NewController(t)
				cache := cacheMocks.NewMockCacheStorage(ctr)
				c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), cache)
				cache.EXPECT().Delete(gomock.Any(), helper.GetLoanDataExistenceCacheKey(tt.args.loanOffer.GetActorId())).Return(nil).AnyTimes()

				got, err := c.Create(epificontext.WithOwnership(tt.args.ctx, k), tt.args.loanOffer)
				if (err != nil) != tt.wantErr {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != nil {
					got.Id = tt.args.loanOffer.GetId()
				}
				got.CreatedAt = nil
				got.UpdatedAt = nil
				if !proto.Equal(got, tt.want) {
					t.Errorf("Create() got = %v,\n want %v", got, tt.want)
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_GetActiveOfferByActorIdAndVendor(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		actorId string
		vendor  preapprovedloanPb.Vendor
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "Successful GetActiveOfferByActorIdAndVendor",
			args: args{
				ctx:     context.Background(),
				actorId: loanOfferSample2.GetActorId(),
				vendor:  loanOfferSample2.GetVendor(),
			},
			want:    loanOfferSample2,
			wantErr: false,
		},
		{
			name: "Record Not Found",
			args: args{
				ctx:     context.Background(),
				actorId: "invalid-actor",
				vendor:  preapprovedloanPb.Vendor_FEDERAL,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "Record Not Found, validity expired",
			args: args{
				ctx:     context.Background(),
				actorId: loanOfferSample3.GetActorId(),
				vendor:  preapprovedloanPb.Vendor_FEDERAL,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				got, err := c.GetActiveOfferByActorIdAndVendor(epificontext.WithOwnership(tt.args.ctx, k), tt.args.actorId, tt.args.vendor)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetActiveOfferByActorIdAndVendor() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != nil {
					got.ValidSince = nil
					got.ValidTill = nil
					got.CreatedAt = nil
					got.UpdatedAt = nil
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetActiveOfferByActorIdAndVendor() got = %v,\n want %v", got, tt.want)
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_GetById(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "Successful GetById",
			args: args{
				ctx: context.Background(),
				id:  loanOfferSample2.GetId(),
			},
			want:    loanOfferSample2,
			wantErr: false,
		},
		{
			name: "Record Not Found",
			args: args{
				ctx: context.Background(),
				id:  "invalid_id",
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				got, err := c.GetById(epificontext.WithOwnership(tt.args.ctx, k), tt.args.id)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != nil {
					got.ValidSince = nil
					got.ValidTill = nil
					got.CreatedAt = nil
					got.UpdatedAt = nil
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetById() got = %v,\n want %v", got, tt.want)
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_Update(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		loanOffer   *preapprovedloanPb.LoanOffer
		updateMasks []preapprovedloanPb.LoanOfferFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		err     error
	}{
		{
			name: "Successful Update",
			args: args{
				ctx:       context.Background(),
				loanOffer: loanOfferSample2,
				updateMasks: []preapprovedloanPb.LoanOfferFieldMask{
					preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID,
					preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS,
				},
			},
			wantErr: false,
		},
		{
			name: "No Rows Updated",
			args: args{
				ctx:       context.Background(),
				loanOffer: loanOfferSample3,
				updateMasks: []preapprovedloanPb.LoanOfferFieldMask{
					preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID,
					preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS,
				},
			},
			wantErr: true,
			err:     epifierrors.ErrRowNotUpdated,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				if err := c.Update(epificontext.WithOwnership(tt.args.ctx, k), tt.args.loanOffer, tt.args.updateMasks); (err != nil) != tt.wantErr {
					t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_DeactivateLoanOffer(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx       context.Context
		loanOffer *preapprovedloanPb.LoanOffer
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Should mark loan offer as deactivated",
			args: args{
				ctx:       context.Background(),
				loanOffer: loanOfferSample2,
			},
			wantErr: false,
		},
		{
			name: "Should fail to mark loan offer as deactivated as no offers exist",
			args: args{
				ctx:       context.Background(),
				loanOffer: &preapprovedloanPb.LoanOffer{Id: "random-id"},
			},
			wantErr: true,
		},
	}
	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)
	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				if err := c.DeactivateLoanOffer(epificontext.WithOwnership(tt.args.ctx, k), tt.args.loanOffer.GetId()); (err != nil) != tt.wantErr {
					t.Errorf("DeactivateLoanOffer() error = %v, wantErr %v", err, tt.wantErr)
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_GetActiveOfferByActorIdAndLoanPrograms(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx          context.Context
		actorId      string
		loanPrograms []preapprovedloanPb.LoanProgram
	}
	tests := []struct {
		name    string
		args    args
		want    []*preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "successfully fetch loan offer",
			args: args{
				ctx:          context.Background(),
				actorId:      loanOfferSample2.GetActorId(),
				loanPrograms: []preapprovedloanPb.LoanProgram{preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN},
			},
			want:    []*preapprovedloanPb.LoanOffer{loanOfferSample2},
			wantErr: false,
		},
		{
			name: "successfully fetch loan offer",
			args: args{
				ctx:          context.Background(),
				actorId:      loanOfferSample2.GetActorId(),
				loanPrograms: []preapprovedloanPb.LoanProgram{preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, preapprovedloanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
			},
			want:    []*preapprovedloanPb.LoanOffer{loanOfferSample2},
			wantErr: false,
		},
		{
			name: "throw error is loan programs is unspecified",
			args: args{
				ctx:          context.Background(),
				actorId:      loanOfferSample2.GetActorId(),
				loanPrograms: []preapprovedloanPb.LoanProgram{preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED},
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "fetched data does not exist",
			args: args{
				ctx:     context.Background(),
				actorId: "random-actor-1",
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			tt, k := tt, k

			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				got, err := c.GetActiveOffersByActorIdAndLoanPrograms(tt.args.ctx, tt.args.actorId, tt.args.loanPrograms)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetActiveOffersByActorIdAndLoanPrograms() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if tt.err != nil {
					if !errors.Is(err, tt.err) && err.Error() != tt.err.Error() {
						t.Errorf("GetActiveOffersByActorIdAndLoanPrograms() error = %v, wantErr %v", err, tt.err)
					}
				}
				require.Equal(t, len(tt.want), len(got))
				for idx := range got {
					gotData, wantData := got[idx], tt.want[idx]
					gotData.ValidSince = nil
					gotData.ValidTill = nil
					gotData.CreatedAt = nil
					gotData.UpdatedAt = nil

					require.True(t, proto.Equal(gotData, wantData), fmt.Sprintf("GetActiveOffersByActorIdAndLoanPrograms() got = %v, want %v", got, tt.want))
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_GetLatestByActorIdAndVendor(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx     context.Context
		actorId string
		vendor  preapprovedloanPb.Vendor
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "Successful GetLatestByActorIdAndVendor",
			args: args{
				ctx:     context.Background(),
				actorId: loanOfferSample2.GetActorId(),
				vendor:  preapprovedloanPb.Vendor_FEDERAL,
			},
			want:    loanOfferSample4,
			wantErr: false,
		},
		{
			name: "Record Not Found",
			args: args{
				ctx:     context.Background(),
				actorId: "invalid_actor_id",
				vendor:  preapprovedloanPb.Vendor_FEDERAL,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				got, err := c.GetLatestByActorIdAndVendor(epificontext.WithOwnership(tt.args.ctx, k), tt.args.actorId, tt.args.vendor)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetLatestByActorIdAndVendor() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				if got != nil {
					got.ValidSince = nil
					got.ValidTill = nil
					got.CreatedAt = nil
					got.UpdatedAt = nil
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetLatestByActorIdAndVendor() got = %v,\n want %v", got, tt.want)
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_GetByOfferEligibilityCriteriaId(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		loecId      string
		count       int
		orderByDesc bool
	}
	tests := []struct {
		name    string
		args    args
		want    []*preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "Invalid loec Id",
			args: args{
				ctx:         context.Background(),
				loecId:      "invalid_id",
				count:       5,
				orderByDesc: false,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "Empty loec Id",
			args: args{
				ctx:         context.Background(),
				loecId:      "",
				count:       5,
				orderByDesc: false,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Successfully got latest",
			args: args{
				ctx:         context.Background(),
				loecId:      "loan-offer-eligibility-criteria-id-1",
				count:       1,
				orderByDesc: true,
			},
			want: []*preapprovedloanPb.LoanOffer{
				loanOfferSample4,
			},
			wantErr: false,
		},
		{
			name: "Successfully got both",
			args: args{
				ctx:         context.Background(),
				loecId:      "loan-offer-eligibility-criteria-id-1",
				count:       5,
				orderByDesc: false,
			},
			want: []*preapprovedloanPb.LoanOffer{
				loanOfferSample2,
				loanOfferSample4,
				loanOfferSample5,
			},
			wantErr: false,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				got, err := c.GetByOfferEligibilityCriteriaId(epificontext.WithOwnership(tt.args.ctx, k), tt.args.loecId, tt.args.count, tt.args.orderByDesc)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetByOfferEligibilityCriteriaId() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if tt.err != nil {
					if !errors.Is(err, tt.err) {
						t.Errorf("GetByOfferEligibilityCriteriaId() tt.error = %v, wantErr %v", err, tt.err)
					}
				}
				for i := range got {
					got[i].ValidSince = nil
					got[i].ValidTill = nil
					got[i].CreatedAt = nil
					got[i].UpdatedAt = nil
				}
				for i := range got {
					if !proto.Equal(got[i], tt.want[i]) {
						t.Errorf("GetByOfferEligibilityCriteriaId() got[%d] = %v, want[%d] = %v", i, got[i], i, tt.want[i])
					}
				}
			})
		}
	}
}

func TestCrdbLoanOfferDao_GetLatestByActorIdVendorAndLoanProgram(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		actorId     string
		vendor      preapprovedloanPb.Vendor
		loanProgram preapprovedloanPb.LoanProgram
	}
	tests := []struct {
		name    string
		args    args
		want    *preapprovedloanPb.LoanOffer
		wantErr bool
		err     error
	}{
		{
			name: "Successful GetLatestByActorIdVendorAndLoanProgram",
			args: args{
				ctx:         context.Background(),
				actorId:     loanOfferSample2.GetActorId(),
				vendor:      preapprovedloanPb.Vendor_FEDERAL,
				loanProgram: preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			},
			want:    loanOfferSample4,
			wantErr: false,
		},
		{
			name: "Record Not Found with different loan program",
			args: args{
				ctx:         context.Background(),
				actorId:     loanOfferSample2.GetActorId(),
				vendor:      preapprovedloanPb.Vendor_FEDERAL,
				loanProgram: preapprovedloanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "Record Not Found, invalid actor id",
			args: args{
				ctx:         context.Background(),
				actorId:     "invalid_actor_id",
				vendor:      preapprovedloanPb.Vendor_FEDERAL,
				loanProgram: preapprovedloanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
	}

	dbResourceProvider, releaseDbProviderFunc := dbResourceProviderPool.GetDbResourceProviderInstance(t)
	defer releaseDbProviderFunc()
	c := impl.NewCrdbLoanOfferDao(dbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), nil)

	for _, tt := range tests {
		for k := range conf.DbConfigMap.GetOwnershipToDbConfigMap() {
			t.Run(fmt.Sprintf("%v DB %v", tt.name, k.String()), func(t *testing.T) {
				got, err := c.GetLatestByActorIdVendorAndLoanProgram(epificontext.WithOwnership(tt.args.ctx, k), tt.args.actorId, tt.args.vendor, tt.args.loanProgram)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetLatestByActorIdVendorAndLoanProgram() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				if got != nil {
					got.ValidSince = nil
					got.ValidTill = nil
					got.CreatedAt = nil
					got.UpdatedAt = nil
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetLatestByActorIdVendorAndLoanProgram() got = %v,\n want %v", got, tt.want)
				}
			})
		}
	}
}
