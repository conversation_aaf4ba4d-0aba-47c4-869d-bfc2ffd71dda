# Script to cherry pick a list of commits to the a release branch named with the "epifi/federal-audit-.*" prefix.
# The script
# - pulls the latest from the release branch
# - then creates a cherry pick branch named "$USER/cp-branch-<date><time>"
# - runs git cherry-pick <commit> for all the commits specified on the command line
# - creates a PR
# The link to the PR can be found in the output
#
# NOTE:
# - The script does not handle conflicts etc very cleanly so do check the diff after it is done
# - Needs gh to be installed to run the PR creation step so installs it at the start
#
# USAGE:
# ./scripts/release/cherrypick.sh -s epifi/federal-audit-m10-rc3 ffc6efcc838202a24bd2 709ed70f2d27411629e 7603bedb4452891c3d

# Install gh only if it is not installed, saves time
brew ls --versions gh || brew install gh

if [ "$1" == "-s" ]; then
    SIGN="-s"
    shift
else
    SIGN=""
fi

RELEASE_BRANCH=${1}
if [[ ! "$1" =~ ^epifi/.*m.*-rc.* ]]; then
    echo "Need a valid release branch name"
    exit 1;
fi

shift

COMMITS="$@"
echo "Number of commits is: ${#COMMITS[@]}"
echo $COMMITS

BODY=$COMMITS
if [ ${#COMMITS[@]} == 0  ]; then
    echo "Need the commit hashes to be cherrypicked"
    exit 1;
fi

CHERRYPICK_BRANCH=${USER}/cp-branch-$(date +"%Y%m%d_%H-%M-%S")
set -x

git fetch
if [ $? -ne 0 ]; then
	echo $COMMITS > ~/.git-cherrypick-resume-commits
	echo "$SIGN" > ~/.git-cherrypick-resume-sign
	exit 1
fi

git checkout --track origin/$RELEASE_BRANCH
if [ $? -ne 0 ]; then
	echo $COMMITS > ~/.git-cherrypick-resume-commits
	echo "$SIGN" > ~/.git-cherrypick-resume-sign
	exit 1
fi

# sync release branch from origin
git pull origin $RELEASE_BRANCH
if [ $? -ne 0 ]; then
	echo $COMMITS > ~/.git-cherrypick-resume-commits
	echo "$SIGN" > ~/.git-cherrypick-resume-sign
	exit 1
fi

# create a branch for the cherrypick
git checkout -b $CHERRYPICK_BRANCH
if [ $? -ne 0 ]; then
	echo $COMMITS > ~/.git-cherrypick-resume-commits
	echo "$SIGN" > ~/.git-cherrypick-resume-sign
	exit 1
fi

if [ "$SIGN" != "-s" ]; then
    SIGN=''
fi

# Cherrypick each specified commit
for commit in $COMMITS
do
	echo "Cherrypicking commit $commit"
    COMMITS=`echo $COMMITS | sed -e "s/.*$commit\s*//"`
    git cherry-pick $SIGN $commit
done

# push to the remote branch
git push origin $CHERRYPICK_BRANCH

# create the PR
gh pr create --title "Cherrypick commits to release branch" -b "Cherrypicking commits $BODY" -B $RELEASE_BRANCH
set +x
exit 0
