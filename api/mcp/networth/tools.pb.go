// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/mcp/networth/tools.proto

package networth

import (
	mutualfund "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	connected_account "github.com/epifi/gamma/api/connected_account"
	epf "github.com/epifi/gamma/api/insights/epf"
	networth "github.com/epifi/gamma/api/insights/networth"
	external "github.com/epifi/gamma/api/investment/mutualfund/external"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NetworthDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetWorthResponse           *networth.GetNetWorthValueResponse               `protobuf:"bytes,1,opt,name=net_worth_response,json=netWorthResponse,proto3" json:"net_worth_response,omitempty"`
	MfSchemeAnalytics          *mutualfund.MfSchemeAnalytics                    `protobuf:"bytes,2,opt,name=mf_scheme_analytics,json=mfSchemeAnalytics,proto3" json:"mf_scheme_analytics,omitempty"`
	AccountDetailsBulkResponse *connected_account.GetAccountDetailsBulkResponse `protobuf:"bytes,3,opt,name=account_details_bulk_response,json=accountDetailsBulkResponse,proto3" json:"account_details_bulk_response,omitempty"`
}

func (x *NetworthDetails) Reset() {
	*x = NetworthDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworthDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworthDetails) ProtoMessage() {}

func (x *NetworthDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworthDetails.ProtoReflect.Descriptor instead.
func (*NetworthDetails) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{0}
}

func (x *NetworthDetails) GetNetWorthResponse() *networth.GetNetWorthValueResponse {
	if x != nil {
		return x.NetWorthResponse
	}
	return nil
}

func (x *NetworthDetails) GetMfSchemeAnalytics() *mutualfund.MfSchemeAnalytics {
	if x != nil {
		return x.MfSchemeAnalytics
	}
	return nil
}

func (x *NetworthDetails) GetAccountDetailsBulkResponse() *connected_account.GetAccountDetailsBulkResponse {
	if x != nil {
		return x.AccountDetailsBulkResponse
	}
	return nil
}

type TransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MutualFundExternalOrders []*external.MutualFundExternalOrder `protobuf:"bytes,1,rep,name=mutual_fund_external_orders,json=mutualFundExternalOrders,proto3" json:"mutual_fund_external_orders,omitempty"`
	UanAccountsDetails       *epf.GetUANAccountsResponse         `protobuf:"bytes,2,opt,name=uan_accounts_details,json=uanAccountsDetails,proto3" json:"uan_accounts_details,omitempty"`
}

func (x *TransactionDetails) Reset() {
	*x = TransactionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetails) ProtoMessage() {}

func (x *TransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetails.ProtoReflect.Descriptor instead.
func (*TransactionDetails) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionDetails) GetMutualFundExternalOrders() []*external.MutualFundExternalOrder {
	if x != nil {
		return x.MutualFundExternalOrders
	}
	return nil
}

func (x *TransactionDetails) GetUanAccountsDetails() *epf.GetUANAccountsResponse {
	if x != nil {
		return x.UanAccountsDetails
	}
	return nil
}

var File_api_mcp_networth_tools_proto protoreflect.FileDescriptor

var file_api_mcp_networth_tools_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x63, 0x70, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c,
	0x6d, 0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x1a, 0x3e, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f,
	0x65, 0x70, 0x66, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc7, 0x02, 0x0a, 0x0f,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x59, 0x0a, 0x12, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x10, 0x6e, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x13, 0x6d, 0x66,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x11, 0x6d,
	0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x12, 0x73, 0x0a, 0x1d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x5f, 0x62, 0x75, 0x6c, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x75, 0x6c,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x75, 0x6c, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe8, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x7a, 0x0a, 0x1b,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x18,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x56, 0x0a, 0x14, 0x75, 0x61, 0x6e, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x65, 0x70, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x41, 0x4e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x75, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5a, 0x27, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x63, 0x70, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_mcp_networth_tools_proto_rawDescOnce sync.Once
	file_api_mcp_networth_tools_proto_rawDescData = file_api_mcp_networth_tools_proto_rawDesc
)

func file_api_mcp_networth_tools_proto_rawDescGZIP() []byte {
	file_api_mcp_networth_tools_proto_rawDescOnce.Do(func() {
		file_api_mcp_networth_tools_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_mcp_networth_tools_proto_rawDescData)
	})
	return file_api_mcp_networth_tools_proto_rawDescData
}

var file_api_mcp_networth_tools_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_mcp_networth_tools_proto_goTypes = []interface{}{
	(*NetworthDetails)(nil),                                 // 0: mcp.networth.NetworthDetails
	(*TransactionDetails)(nil),                              // 1: mcp.networth.TransactionDetails
	(*networth.GetNetWorthValueResponse)(nil),               // 2: insights.networth.GetNetWorthValueResponse
	(*mutualfund.MfSchemeAnalytics)(nil),                    // 3: api.analyser.variables.mutualfund.MfSchemeAnalytics
	(*connected_account.GetAccountDetailsBulkResponse)(nil), // 4: connected_account.GetAccountDetailsBulkResponse
	(*external.MutualFundExternalOrder)(nil),                // 5: api.investment.mutualfund.external.MutualFundExternalOrder
	(*epf.GetUANAccountsResponse)(nil),                      // 6: insights.epf.GetUANAccountsResponse
}
var file_api_mcp_networth_tools_proto_depIdxs = []int32{
	2, // 0: mcp.networth.NetworthDetails.net_worth_response:type_name -> insights.networth.GetNetWorthValueResponse
	3, // 1: mcp.networth.NetworthDetails.mf_scheme_analytics:type_name -> api.analyser.variables.mutualfund.MfSchemeAnalytics
	4, // 2: mcp.networth.NetworthDetails.account_details_bulk_response:type_name -> connected_account.GetAccountDetailsBulkResponse
	5, // 3: mcp.networth.TransactionDetails.mutual_fund_external_orders:type_name -> api.investment.mutualfund.external.MutualFundExternalOrder
	6, // 4: mcp.networth.TransactionDetails.uan_accounts_details:type_name -> insights.epf.GetUANAccountsResponse
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_mcp_networth_tools_proto_init() }
func file_api_mcp_networth_tools_proto_init() {
	if File_api_mcp_networth_tools_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_mcp_networth_tools_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworthDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_mcp_networth_tools_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_mcp_networth_tools_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_mcp_networth_tools_proto_goTypes,
		DependencyIndexes: file_api_mcp_networth_tools_proto_depIdxs,
		MessageInfos:      file_api_mcp_networth_tools_proto_msgTypes,
	}.Build()
	File_api_mcp_networth_tools_proto = out.File
	file_api_mcp_networth_tools_proto_rawDesc = nil
	file_api_mcp_networth_tools_proto_goTypes = nil
	file_api_mcp_networth_tools_proto_depIdxs = nil
}
