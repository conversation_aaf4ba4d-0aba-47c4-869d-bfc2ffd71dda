// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/mcp/networth/tools.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NetworthDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NetworthDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetworthDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetworthDetailsMultiError, or nil if none found.
func (m *NetworthDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NetworthDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetWorthResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "NetWorthResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "NetWorthResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetWorthResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthDetailsValidationError{
				field:  "NetWorthResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMfSchemeAnalytics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "MfSchemeAnalytics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "MfSchemeAnalytics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMfSchemeAnalytics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthDetailsValidationError{
				field:  "MfSchemeAnalytics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountDetailsBulkResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "AccountDetailsBulkResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "AccountDetailsBulkResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetailsBulkResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthDetailsValidationError{
				field:  "AccountDetailsBulkResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NetworthDetailsMultiError(errors)
	}

	return nil
}

// NetworthDetailsMultiError is an error wrapping multiple validation errors
// returned by NetworthDetails.ValidateAll() if the designated constraints
// aren't met.
type NetworthDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetworthDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetworthDetailsMultiError) AllErrors() []error { return m }

// NetworthDetailsValidationError is the validation error returned by
// NetworthDetails.Validate if the designated constraints aren't met.
type NetworthDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetworthDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetworthDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetworthDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetworthDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetworthDetailsValidationError) ErrorName() string { return "NetworthDetailsValidationError" }

// Error satisfies the builtin error interface
func (e NetworthDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetworthDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetworthDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetworthDetailsValidationError{}

// Validate checks the field values on TransactionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionDetailsMultiError, or nil if none found.
func (m *TransactionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMutualFundExternalOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TransactionDetailsValidationError{
						field:  fmt.Sprintf("MutualFundExternalOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TransactionDetailsValidationError{
						field:  fmt.Sprintf("MutualFundExternalOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TransactionDetailsValidationError{
					field:  fmt.Sprintf("MutualFundExternalOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUanAccountsDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionDetailsValidationError{
					field:  "UanAccountsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionDetailsValidationError{
					field:  "UanAccountsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUanAccountsDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionDetailsValidationError{
				field:  "UanAccountsDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionDetailsMultiError(errors)
	}

	return nil
}

// TransactionDetailsMultiError is an error wrapping multiple validation errors
// returned by TransactionDetails.ValidateAll() if the designated constraints
// aren't met.
type TransactionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionDetailsMultiError) AllErrors() []error { return m }

// TransactionDetailsValidationError is the validation error returned by
// TransactionDetails.Validate if the designated constraints aren't met.
type TransactionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionDetailsValidationError) ErrorName() string {
	return "TransactionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionDetailsValidationError{}
